{"name": "@repo/api-client", "version": "0.0.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/types": "workspace:*"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.3"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}