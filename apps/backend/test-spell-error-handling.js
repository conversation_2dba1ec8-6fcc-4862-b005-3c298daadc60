// Simple test script to verify spell error handling
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000/api';

async function testSpellErrorHandling() {
  console.log('Testing spell error handling...\n');

  // Test 1: Create a spell successfully
  console.log('1. Creating a test spell...');
  const testSpell = {
    name: 'Test Spell for Error Handling',
    convocation: 'Neutral',
    complexityLevel: 1,
    description: 'A test spell to verify error handling',
    bonusEffects: [],
    castingTime: '1 action',
    range: 'Touch',
    duration: 'Instantaneous',
    folderId: 1,
    sourceBook: 'Test Book',
    sourcePage: '1'
  };

  try {
    const response = await fetch(`${BASE_URL}/spells`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testSpell),
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✓ Spell created successfully:', result.name);
      console.log('  Spell ID:', result.id);
    } else {
      const error = await response.json();
      console.log('✗ Failed to create spell:', error);
      return;
    }
  } catch (error) {
    console.log('✗ Error creating spell:', error.message);
    return;
  }

  // Test 2: Try to create a spell with the same name (should fail)
  console.log('\n2. Attempting to create a spell with duplicate name...');
  try {
    const response = await fetch(`${BASE_URL}/spells`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testSpell),
    });

    if (response.status === 409) {
      const error = await response.json();
      console.log('✓ Correctly rejected duplicate spell name');
      console.log('  Error message:', error.error);
      console.log('  Error code:', error.code);
      console.log('  Error details:', error.details);
    } else {
      console.log('✗ Expected 409 Conflict, got:', response.status);
      const result = await response.json();
      console.log('  Response:', result);
    }
  } catch (error) {
    console.log('✗ Error testing duplicate:', error.message);
  }

  // Test 3: Test import with duplicates
  console.log('\n3. Testing import with duplicate names...');
  const importSpells = [
    {
      name: 'Import Test Spell 1',
      convocation: 'Neutral',
      complexityLevel: 1,
      description: 'First import test spell',
      bonusEffects: [],
      castingTime: '1 action',
      range: 'Touch',
      duration: 'Instantaneous',
      folderId: 1,
      sourceBook: 'Test Book',
      sourcePage: '2'
    },
    {
      name: 'Test Spell for Error Handling', // This should conflict with existing spell
      convocation: 'Neutral',
      complexityLevel: 2,
      description: 'Duplicate name test',
      bonusEffects: [],
      castingTime: '1 action',
      range: 'Touch',
      duration: 'Instantaneous',
      folderId: 1,
      sourceBook: 'Test Book',
      sourcePage: '3'
    },
    {
      name: 'Import Test Spell 2',
      convocation: 'Neutral',
      complexityLevel: 1,
      description: 'Second import test spell',
      bonusEffects: [],
      castingTime: '1 action',
      range: 'Touch',
      duration: 'Instantaneous',
      folderId: 1,
      sourceBook: 'Test Book',
      sourcePage: '4'
    }
  ];

  try {
    const response = await fetch(`${BASE_URL}/spells/import`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ spells: importSpells }),
    });

    const result = await response.json();
    console.log('Import result status:', response.status);
    console.log('Import result:', result);

    if (result.errors && result.errors.length > 0) {
      console.log('✓ Import correctly handled errors:');
      result.errors.forEach((error, index) => {
        console.log(`  Error ${index + 1}: ${error.error}`);
      });
    }
  } catch (error) {
    console.log('✗ Error testing import:', error.message);
  }

  console.log('\nTest completed!');
}

// Run the test if this script is executed directly
if (require.main === module) {
  testSpellErrorHandling().catch(console.error);
}

module.exports = { testSpellErrorHandling };
