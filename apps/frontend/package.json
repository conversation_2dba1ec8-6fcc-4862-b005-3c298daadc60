{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "start": "vite preview", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@repo/api-client": "workspace:*", "@repo/types": "workspace:*", "@tailwindcss/vite": "^4.1.11", "marked": "^16.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/marked": "^6.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}